#!/usr/bin/env python3
"""
Test script to verify header-data alignment fix
"""

import pandas as pd
from data_pipeline import TransformationEngine, TransformationRule

def test_alignment_fix():
    """Test that headers and data are properly aligned"""

    print("=== Testing Header-Data Alignment Fix ===")

    # Create sample data that mimics a real file
    # This represents raw data from a pipe-delimited file
    sample_data = pd.DataFrame([
        ['35000', '26009', 'OPTIDX', 'BANKNIFTY', 'XX', '', '**********', '3200000', 'CE'],
        ['35001', '26010', 'OPTIDX', 'BANKNIFTY', 'YY', '', '**********', '1600000', 'PE'],
        ['35002', '26011', 'OPTIDX', 'NIFTY', 'ZZ', '', '**********', '800000', 'CE']
    ])

    print(f"Original data shape: {sample_data.shape}")
    print("Original data (first 3 columns):")
    print(sample_data.iloc[:, :3].to_string())

    # Create transformation rules that match the SR sheet structure
    # This simulates the rules from "Sr. No. 1" sheet
    rules = [
        TransformationRule("Token", True, "Text", "-"),                    # Column 0 -> Keep
        TransformationRule("Instrument", True, "Text", "-"),               # Column 1 -> Keep
        TransformationRule("Symbol", True, "Text", "-"),                   # Column 2 -> Keep
        TransformationRule("Series", True, "Text", "-"),                   # Column 3 -> Keep
        TransformationRule("Option_Type", True, "Text", "-"),              # Column 4 -> Keep
        TransformationRule("Unused_Field", False, "Text", "-"),            # Column 5 -> Drop
        TransformationRule("Expiry_Date", True, "Date", "Convert to date"), # Column 6 -> Keep & Transform
        TransformationRule("Strike_Price", True, "Number", "Divide by 100"), # Column 7 -> Keep & Transform
        TransformationRule("Call_Put", True, "Text", "-"),                 # Column 8 -> Keep
    ]

    print(f"\nTransformation rules:")
    for i, rule in enumerate(rules):
        status = "KEEP" if rule.required else "DROP"
        action = f" + {rule.special_action}" if rule.special_action != "-" else ""
        print(f"  Col {i}: {rule.header_name} -> {status}{action}")

    # Apply transformations using the corrected logic
    engine = TransformationEngine()
    df_transformed, columns_dropped, columns_transformed = engine.apply_transformations(sample_data, rules)

    print(f"\nTransformed data shape: {df_transformed.shape}")
    print(f"Columns dropped: {columns_dropped}")
    print(f"Columns transformed: {columns_transformed}")

    print("\nFinal transformed data:")
    print(df_transformed.to_string())

    # Verify alignment
    print("\n=== Verification ===")
    print("Checking if data is correctly aligned under headers:")

    # Check Token column (should be 35000, 35001, 35002)
    token_values = df_transformed['Token'].tolist()
    print(f"Token column: {token_values}")
    expected_tokens = ['35000', '35001', '35002']
    if token_values == expected_tokens:
        print("✓ Token column correctly aligned")
    else:
        print("✗ Token column misaligned!")

    # Check Strike_Price column (should be transformed: 32000, 16000, 8000)
    strike_values = df_transformed['Strike_Price'].tolist()
    print(f"Strike_Price column: {strike_values}")
    expected_strikes = ['32000', '16000', '8000']  # After divide by 100
    if strike_values == expected_strikes:
        print("✓ Strike_Price column correctly aligned and transformed")
    else:
        print("✗ Strike_Price column misaligned or not transformed!")

    # Check Expiry_Date column (should be transformed to DD-MMM-YY format)
    expiry_values = df_transformed['Expiry_Date'].tolist()
    print(f"Expiry_Date column: {expiry_values}")
    if all('AUG' in val or 'APR' in val or 'JUL' in val for val in expiry_values if val):
        print("✓ Expiry_Date column correctly transformed")
    else:
        print("✗ Expiry_Date column not properly transformed!")

    # Verify that unused field was dropped
    if 'Unused_Field' not in df_transformed.columns:
        print("✓ Unused_Field correctly dropped")
    else:
        print("✗ Unused_Field was not dropped!")

    print("\n=== Summary ===")
    print(f"Expected columns: 8 (dropped 1 out of 9)")
    print(f"Actual columns: {len(df_transformed.columns)}")
    print(f"Column names: {list(df_transformed.columns)}")

    if len(df_transformed.columns) == 8 and 'Token' in df_transformed.columns:
        print("✅ Header-data alignment fix is working correctly!")
        return True
    else:
        print("❌ Header-data alignment still has issues!")
        return False

if __name__ == "__main__":
    success = test_alignment_fix()
    exit(0 if success else 1)