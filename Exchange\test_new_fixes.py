#!/usr/bin/env python3
"""
Test script for the two specific fixes:
1. Addition of Exchange column
2. Removal of duplicate headers
"""

import pandas as pd
import tempfile
import os
from pathlib import Path
from data_pipeline import (
    DataReader, TransformationEngine, MasterExcelReader, 
    FileInfo, TransformationRule
)

def test_exchange_column_addition():
    """Test that Exchange column is added as the first column"""
    print("=== Testing Exchange Column Addition ===")
    
    try:
        # Create sample data
        sample_data = pd.DataFrame({
            'col1': ['value1', 'value2', 'value3'],
            'col2': ['value4', 'value5', 'value6'],
            'col3': ['value7', 'value8', 'value9']
        })
        
        # Create sample transformation rules
        rules = [
            TransformationRule(header_name='Header1', required=True, format_type='text', special_action='-'),
            TransformationRule(header_name='Header2', required=True, format_type='text', special_action='-'),
            TransformationRule(header_name='Header3', required=False, format_type='text', special_action='-')
        ]
        
        # Create file info with exchange value
        file_info = FileInfo(
            sr_number="1",
            file_name="test.txt",
            file_location="test_location",
            exchange="NSE"  # This should be added as first column
        )
        
        # Apply transformations
        engine = TransformationEngine()
        df_transformed, columns_dropped, columns_transformed = engine.apply_transformations(
            sample_data, rules, file_info
        )
        
        print(f"Original data shape: {sample_data.shape}")
        print(f"Transformed data shape: {df_transformed.shape}")
        print(f"Columns: {list(df_transformed.columns)}")
        
        # Verify Exchange column is first
        if df_transformed.columns[0] == 'Exchange':
            print("✓ Exchange column is correctly placed as first column")
            
            # Verify all values in Exchange column are correct
            exchange_values = df_transformed['Exchange'].unique()
            if len(exchange_values) == 1 and exchange_values[0] == 'NSE':
                print("✓ Exchange column has correct value 'NSE' for all rows")
            else:
                print(f"✗ Exchange column has incorrect values: {exchange_values}")
        else:
            print(f"✗ Exchange column is not first. First column is: {df_transformed.columns[0]}")
        
        print("\nTransformed DataFrame:")
        print(df_transformed.to_string())
        
    except Exception as e:
        print(f"Error testing Exchange column addition: {e}")
    
    print()

def test_duplicate_header_removal():
    """Test that duplicate headers are detected and removed"""
    print("=== Testing Duplicate Header Removal ===")
    
    try:
        # Create a temporary file with duplicate headers
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            # Write duplicate headers
            f.write("Header1,Header2,Header3\n")  # First row (header)
            f.write("Header1,Header2,Header3\n")  # Second row (duplicate header)
            f.write("value1,value2,value3\n")     # Actual data
            f.write("value4,value5,value6\n")     # More data
            temp_file_path = f.name
        
        try:
            # Read the file using DataReader
            reader = DataReader()
            df = reader.read_file(temp_file_path)
            
            print(f"Data shape after reading: {df.shape}")
            print("DataFrame content:")
            print(df.to_string())
            
            # Check if duplicate header was removed
            # The file had 4 rows originally, should now have 3 rows (duplicate removed)
            if df.shape[0] == 3:
                print("✓ Duplicate header row was successfully removed")
                
                # Check that the first row contains the headers, not duplicated
                first_row = df.iloc[0].astype(str).tolist()
                second_row = df.iloc[1].astype(str).tolist()
                
                if first_row != second_row:
                    print("✓ First two rows are now different (no duplicate headers)")
                else:
                    print("✗ First two rows are still identical")
            else:
                print(f"✗ Expected 3 rows after duplicate removal, got {df.shape[0]} rows")
                
        finally:
            # Clean up temporary file
            os.unlink(temp_file_path)
            
    except Exception as e:
        print(f"Error testing duplicate header removal: {e}")
    
    print()

def test_no_duplicate_headers():
    """Test that normal files without duplicate headers are not affected"""
    print("=== Testing Normal File (No Duplicate Headers) ===")
    
    try:
        # Create a temporary file without duplicate headers
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write("Header1,Header2,Header3\n")  # Header row
            f.write("value1,value2,value3\n")     # Data row 1
            f.write("value4,value5,value6\n")     # Data row 2
            temp_file_path = f.name
        
        try:
            # Read the file using DataReader
            reader = DataReader()
            df = reader.read_file(temp_file_path)
            
            print(f"Data shape after reading: {df.shape}")
            print("DataFrame content:")
            print(df.to_string())
            
            # Check that no rows were removed (should still have 3 rows)
            if df.shape[0] == 3:
                print("✓ No rows were incorrectly removed from normal file")
            else:
                print(f"✗ Expected 3 rows, got {df.shape[0]} rows")
                
        finally:
            # Clean up temporary file
            os.unlink(temp_file_path)
            
    except Exception as e:
        print(f"Error testing normal file: {e}")
    
    print()

def test_integration():
    """Test both fixes working together"""
    print("=== Testing Integration (Both Fixes Together) ===")
    
    try:
        # Create a temporary file with duplicate headers
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write("Col1,Col2,Col3\n")     # First row (header)
            f.write("Col1,Col2,Col3\n")     # Second row (duplicate header)
            f.write("data1,data2,data3\n")  # Actual data
            f.write("data4,data5,data6\n")  # More data
            temp_file_path = f.name
        
        try:
            # Read the file
            reader = DataReader()
            df = reader.read_file(temp_file_path)
            
            # Create transformation rules
            rules = [
                TransformationRule(header_name='Header1', required=True, format_type='text', special_action='-'),
                TransformationRule(header_name='Header2', required=True, format_type='text', special_action='-'),
                TransformationRule(header_name='Header3', required=False, format_type='text', special_action='-')
            ]
            
            # Create file info with exchange
            file_info = FileInfo(
                sr_number="1",
                file_name="test.csv",
                file_location=temp_file_path,
                exchange="BSE"
            )
            
            # Apply transformations
            engine = TransformationEngine()
            df_transformed, columns_dropped, columns_transformed = engine.apply_transformations(
                df, rules, file_info
            )
            
            print(f"Final data shape: {df_transformed.shape}")
            print("Final DataFrame:")
            print(df_transformed.to_string())
            
            # Verify both fixes
            success = True
            
            # Check Exchange column
            if df_transformed.columns[0] == 'Exchange':
                print("✓ Exchange column is first")
                if all(df_transformed['Exchange'] == 'BSE'):
                    print("✓ Exchange column has correct value 'BSE'")
                else:
                    print("✗ Exchange column has incorrect values")
                    success = False
            else:
                print("✗ Exchange column is not first")
                success = False
            
            # Check that we have the expected number of data rows
            # Original file had 4 rows, after duplicate removal should have 3 rows
            # (including the original header row which becomes data)
            if df_transformed.shape[0] == 3:
                print("✓ Correct number of data rows after duplicate header removal")
            else:
                print(f"✗ Expected 3 data rows, got {df_transformed.shape[0]}")
                success = False
            
            if success:
                print("✓ Integration test passed - both fixes working correctly!")
            else:
                print("✗ Integration test failed")
                
        finally:
            # Clean up temporary file
            os.unlink(temp_file_path)
            
    except Exception as e:
        print(f"Error in integration test: {e}")
    
    print()

if __name__ == "__main__":
    print("Testing the two specific fixes for output file generation\n")
    
    test_exchange_column_addition()
    test_duplicate_header_removal()
    test_no_duplicate_headers()
    test_integration()
    
    print("All tests completed!")
