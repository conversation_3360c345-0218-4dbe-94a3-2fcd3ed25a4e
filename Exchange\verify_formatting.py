#!/usr/bin/env python3
"""
Verify that formatting is being applied to output files correctly
"""

import openpyxl
from pathlib import Path
import sys

def verify_formatting_applied(file_path):
    """Verify that formatting has been applied to an Excel file"""
    try:
        wb = openpyxl.load_workbook(file_path)
        
        # Check if 'Data' sheet exists (our formatted files have this)
        if 'Data' not in wb.sheetnames:
            print(f"❌ {file_path.name}: No 'Data' sheet found")
            return False
        
        ws = wb['Data']
        
        # Check for proper formatting indicators
        formatting_indicators = []
        
        # Check for percentage formatting
        for row in ws.iter_rows(min_row=2, max_row=min(10, ws.max_row)):
            for cell in row:
                if cell.number_format and '%' in cell.number_format:
                    formatting_indicators.append(f"Percentage format: {cell.coordinate}")
                elif cell.number_format and cell.number_format in ['0', '0.00']:
                    formatting_indicators.append(f"Number format: {cell.coordinate}")
                elif cell.number_format == '@':
                    formatting_indicators.append(f"Text format: {cell.coordinate}")
        
        # Check for calculated columns (like "Long: Final Margin (%)")
        headers = [cell.value for cell in ws[1] if cell.value]
        calculated_columns = [h for h in headers if "Final Margin" in str(h)]
        
        wb.close()
        
        print(f"✅ {file_path.name}:")
        print(f"   Headers: {len(headers)} columns")
        if formatting_indicators:
            print(f"   Formatting applied: {len(formatting_indicators)} cells")
            # Show first few examples
            for indicator in formatting_indicators[:3]:
                print(f"     - {indicator}")
            if len(formatting_indicators) > 3:
                print(f"     ... and {len(formatting_indicators) - 3} more")
        if calculated_columns:
            print(f"   Calculated columns: {calculated_columns}")
        
        return True
        
    except Exception as e:
        print(f"❌ {file_path.name}: Error reading file - {e}")
        return False

def main():
    """Main verification function"""
    print("FORMATTING VERIFICATION")
    print("=" * 50)
    
    # Check op_fresh directory (where your files are)
    output_dir = Path("op_fresh")
    
    if not output_dir.exists():
        print(f"❌ Output directory not found: {output_dir}")
        return 1
    
    # Find Excel files (excluding processing summaries)
    excel_files = [f for f in output_dir.glob("*.xlsx") 
                   if not f.name.startswith("processing_summary") 
                   and not f.name.startswith("formatting_summary")]
    
    if not excel_files:
        print(f"❌ No Excel files found in {output_dir}")
        return 1
    
    print(f"Found {len(excel_files)} Excel files to verify:")
    print()
    
    success_count = 0
    for excel_file in excel_files:
        if verify_formatting_applied(excel_file):
            success_count += 1
        print()
    
    print("=" * 50)
    print(f"VERIFICATION SUMMARY: {success_count}/{len(excel_files)} files properly formatted")
    
    if success_count == len(excel_files):
        print("🎉 All files have formatting applied correctly!")
        print("\nFormatting is working as expected:")
        print("- ✅ Applied to OUTPUT files (generated files)")
        print("- ✅ Proper Excel formatting (percentages, numbers, text)")
        print("- ✅ Calculated columns added where specified")
    else:
        print("⚠️  Some files may not have formatting applied")
    
    return 0 if success_count == len(excel_files) else 1

if __name__ == "__main__":
    sys.exit(main())
