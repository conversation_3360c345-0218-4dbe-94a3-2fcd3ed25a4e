#!/usr/bin/env python3
"""
Build script for creating executables

This script automates the process of building standalone executables
for both CLI and GUI versions of the data processing pipeline.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{description}...")
    print(f"Running: {command}")

    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print("✓ Success")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def check_dependencies():
    """Check if required dependencies are installed"""
    print("Checking dependencies...")

    required_packages = ['pandas', 'openpyxl', 'chardet', 'pyinstaller']
    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} (missing)")
            missing_packages.append(package)

    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Install them with: pip install " + " ".join(missing_packages))
        return False

    return True

def clean_build_dirs():
    """Clean previous build directories"""
    print("\nCleaning previous builds...")

    dirs_to_clean = ['build', 'dist', '__pycache__']

    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"✓ Removed {dir_name}")

    # Clean .pyc files
    for pyc_file in Path('.').rglob('*.pyc'):
        pyc_file.unlink()

    print("✓ Cleaned build directories")

def build_cli():
    """Build CLI executable"""
    command = (
        "pyinstaller --onefile --console "
        "--name data_processor_cli "
        "--add-data \"requirements.txt;.\" "
        "cli.py"
    )

    return run_command(command, "Building CLI executable")

def build_gui():
    """Build GUI executable"""
    command = (
        "pyinstaller --onefile --windowed "
        "--name data_processor_gui "
        "--add-data \"requirements.txt;.\" "
        "gui.py"
    )

    return run_command(command, "Building GUI executable")

def create_distribution():
    """Create distribution package"""
    print("\nCreating distribution package...")

    # Create distribution directory
    dist_dir = Path("distribution")
    dist_dir.mkdir(exist_ok=True)

    # Copy executables
    exe_files = list(Path("dist").glob("*.exe"))
    for exe_file in exe_files:
        shutil.copy2(exe_file, dist_dir)
        print(f"✓ Copied {exe_file.name}")

    # Copy documentation and configuration files
    files_to_copy = [
        "README.md",
        "requirements.txt",
        "BRD file Automation.xlsx"
    ]

    for file_name in files_to_copy:
        if Path(file_name).exists():
            shutil.copy2(file_name, dist_dir)
            print(f"✓ Copied {file_name}")

    # Create sample directory
    sample_dir = dist_dir / "samples"
    sample_dir.mkdir(exist_ok=True)

    # Copy reference files if they exist
    ref_dir = Path("Reference Files for Understanding")
    if ref_dir.exists():
        shutil.copytree(ref_dir, sample_dir / "Reference Files", dirs_exist_ok=True)
        print("✓ Copied reference files")

    print(f"✓ Distribution package created in: {dist_dir}")
    return True

def main():
    """Main build function"""
    print("=" * 60)
    print("DATA PROCESSING PIPELINE - BUILD SCRIPT")
    print("=" * 60)

    # Check dependencies
    if not check_dependencies():
        print("\n❌ Build failed: Missing dependencies")
        return 1

    # Clean previous builds
    clean_build_dirs()

    # Build executables
    cli_success = build_cli()
    gui_success = build_gui()

    if not (cli_success and gui_success):
        print("\n❌ Build failed: Error creating executables")
        return 1

    # Create distribution package
    if not create_distribution():
        print("\n❌ Build failed: Error creating distribution")
        return 1

    # Summary
    print("\n" + "=" * 60)
    print("BUILD COMPLETED SUCCESSFULLY")
    print("=" * 60)

    print("\nGenerated files:")
    dist_files = list(Path("distribution").iterdir())
    for file_path in sorted(dist_files):
        if file_path.is_file():
            size_mb = file_path.stat().st_size / (1024 * 1024)
            print(f"  📄 {file_path.name} ({size_mb:.1f} MB)")
        else:
            print(f"  📁 {file_path.name}/")

    print(f"\nDistribution ready in: {Path('distribution').absolute()}")
    print("\nUsage:")
    print("  CLI: data_processor_cli.exe -e master.xlsx")
    print("  GUI: data_processor_gui.exe")

    return 0

if __name__ == "__main__":
    sys.exit(main())