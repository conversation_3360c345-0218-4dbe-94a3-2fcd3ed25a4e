#!/usr/bin/env python3
"""
Test script for the full pipeline
"""

import sys
from pathlib import Path
from data_pipeline import DataProcessingPipeline, FileInfo

def test_single_file():
    """Test processing a single file"""

    print("=== Testing Full Pipeline with Single File ===")

    # Create pipeline
    pipeline = DataProcessingPipeline("BRD file Automation.xlsx", "test_output")

    # Create a test file info (simulating SR 1 with contract.txt)
    test_file = FileInfo(
        sr_number="1",
        file_name="contract.txt",
        file_location="Reference Files for Understanding/contract.txt"
    )

    # Process the single file
    result = pipeline.process_single_file(test_file)

    print(f"Processing result:")
    print(f"  Status: {result.status}")
    print(f"  Errors: {result.errors}")
    print(f"  Columns dropped: {len(result.columns_dropped)}")
    print(f"  Columns transformed: {len(result.columns_transformed)}")
    print(f"  Output file: {result.output_file}")
    print(f"  Timestamp: {result.timestamp}")

    if result.output_file and Path(result.output_file).exists():
        print(f"\nOutput file created successfully: {result.output_file}")
        print(f"File size: {Path(result.output_file).stat().st_size} bytes")

    return result

def test_full_pipeline():
    """Test the complete pipeline"""

    print("\n=== Testing Complete Pipeline ===")

    # Create pipeline
    pipeline = DataProcessingPipeline("BRD file Automation.xlsx", "test_output")

    # Read file list from Excel
    files = pipeline.master_excel_reader.read_file_list()
    print(f"Found {len(files)} files in master Excel:")

    for i, file_info in enumerate(files[:5]):  # Show first 5
        print(f"  {i+1}. SR {file_info.sr_number}: {file_info.file_name}")
        print(f"      Location: {file_info.file_location}")

    # Process only the first file for testing
    if files:
        print(f"\nProcessing first file: {files[0].file_name}")
        result = pipeline.process_single_file(files[0])

        print(f"Result: {result.status}")
        if result.errors:
            for error in result.errors:
                print(f"  Error: {error}")

        if result.output_file:
            print(f"  Output: {result.output_file}")

if __name__ == "__main__":
    # Test single file first
    single_result = test_single_file()

    # Test full pipeline
    test_full_pipeline()