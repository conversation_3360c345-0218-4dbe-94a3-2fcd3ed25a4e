#!/usr/bin/env python3
"""
Test script to verify the two additional fixes:
1. SR Sheet Reference Error (Formula Evaluation)
2. Format Type Enforcement for Excel Output
"""

import pandas as pd
import openpyxl
from pathlib import Path
from data_pipeline import MasterExcelReader, TransformationRule, OutputGenerator, FileInfo

def test_sr_number_extraction():
    """Test the SR number extraction functionality"""
    print("=== Testing SR Number Extraction ===")
    
    reader = MasterExcelReader("BRD file Automation.xlsx")
    
    # Test the _extract_sr_number method with various inputs
    test_cases = [
        ("1", "1"),           # Simple integer
        ("2.0", "2"),         # Float from Excel
        ("=A5+1", "1"),       # Would be evaluated by Excel (if A5=0)
        ("Sr. 3", "3"),       # Text with number
        ("", ""),             # Empty
        ("ABC", "ABC"),       # No numbers
    ]
    
    print("Testing SR number extraction:")
    for input_val, expected in test_cases:
        result = reader._extract_sr_number(input_val)
        status = "✓" if result == expected else "✗"
        print(f"  {status} Input: '{input_val}' → Output: '{result}' (Expected: '{expected}')")
    
    print()

def test_excel_formula_evaluation():
    """Test that Excel formulas are properly evaluated"""
    print("=== Testing Excel Formula Evaluation ===")
    
    try:
        # Test reading the actual master Excel file
        reader = MasterExcelReader("BRD file Automation.xlsx")
        files = reader.read_file_list()
        
        print(f"Found {len(files)} files in master Excel:")
        for i, file_info in enumerate(files[:5]):  # Show first 5
            print(f"  {i+1}. SR {file_info.sr_number}: {file_info.file_name}")
        
        # Check if we can read transformation rules for different SR numbers
        unique_sr_numbers = set(f.sr_number for f in files if f.sr_number)
        print(f"\nUnique SR numbers found: {sorted(unique_sr_numbers)}")
        
        # Try to read rules for each SR number
        for sr_num in sorted(unique_sr_numbers)[:3]:  # Test first 3
            rules = reader.read_transformation_rules(sr_num)
            status = "✓" if rules else "✗"
            print(f"  {status} SR {sr_num}: Found {len(rules)} transformation rules")
            
    except Exception as e:
        print(f"Error testing Excel formula evaluation: {e}")
    
    print()

def test_excel_cell_formatting():
    """Test Excel cell formatting with different data types"""
    print("=== Testing Excel Cell Formatting ===")
    
    # Create test DataFrame with mixed data types
    test_df = pd.DataFrame({
        'text_column': ['ABC123', '456DEF', 'TEXT'],
        'number_column': ['123', '456.78', '999'],
        'mixed_column': ['100', 'TEXT', '200.5']
    })
    
    # Create format information
    format_info = {
        'text_column': 'Text',
        'number_column': 'Number',
        'mixed_column': 'Number'
    }
    
    print("Test DataFrame:")
    print(test_df)
    print(f"\nFormat specifications: {format_info}")
    
    # Create OutputGenerator and test file info
    output_gen = OutputGenerator("test_output")
    file_info = FileInfo(
        sr_number="TEST",
        file_name="format_test.txt",
        file_location="test_location"
    )
    
    try:
        # Generate output file with format information
        output_file = output_gen.generate_output_file(
            test_df, file_info, [], [], format_info
        )
        print(f"\nOutput file created: {output_file}")
        
        # Verify the Excel file was created with proper formatting
        if Path(output_file).exists():
            print("✓ Excel file created successfully")
            
            # Read back the Excel file to verify formatting
            wb = openpyxl.load_workbook(output_file)
            ws = wb['Data']
            
            print("\nVerifying Excel cell types:")
            headers = [cell.value for cell in ws[1]]
            
            for col_idx, header in enumerate(headers):
                if header in format_info:
                    col_letter = chr(65 + col_idx)
                    cell = ws[f"{col_letter}2"]  # Check first data row
                    
                    expected_format = format_info[header].lower()
                    cell_type = type(cell.value).__name__
                    number_format = cell.number_format
                    
                    print(f"  Column '{header}' (Expected: {expected_format}):")
                    print(f"    Cell value: {cell.value} (Type: {cell_type})")
                    print(f"    Number format: {number_format}")
            
            wb.close()
        else:
            print("✗ Excel file was not created")
            
    except Exception as e:
        print(f"Error testing Excel cell formatting: {e}")
    
    print()

def test_integration():
    """Test integration of both fixes together"""
    print("=== Testing Integration ===")
    
    try:
        # Test that we can process multiple SR numbers
        reader = MasterExcelReader("BRD file Automation.xlsx")
        files = reader.read_file_list()
        
        # Count how many different SR numbers we can successfully read rules for
        successful_sr_reads = 0
        total_unique_srs = 0
        
        unique_sr_numbers = set(f.sr_number for f in files if f.sr_number)
        total_unique_srs = len(unique_sr_numbers)
        
        for sr_num in unique_sr_numbers:
            rules = reader.read_transformation_rules(sr_num)
            if rules:
                successful_sr_reads += 1
        
        print(f"SR Number Processing Results:")
        print(f"  Total unique SR numbers: {total_unique_srs}")
        print(f"  Successfully read rules: {successful_sr_reads}")
        print(f"  Success rate: {(successful_sr_reads/total_unique_srs)*100:.1f}%" if total_unique_srs > 0 else "0%")
        
        if successful_sr_reads > 1:
            print("✓ Multiple SR sheets are now accessible (formula evaluation working)")
        else:
            print("✗ Only one or no SR sheets accessible")
            
    except Exception as e:
        print(f"Error in integration test: {e}")
    
    print()

if __name__ == "__main__":
    print("Testing Additional Fixes")
    print("=" * 50)
    
    test_sr_number_extraction()
    test_excel_formula_evaluation()
    test_excel_cell_formatting()
    test_integration()
    
    print("All additional tests completed!")
