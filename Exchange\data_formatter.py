#!/usr/bin/env python3
"""
Data Formatting Module

This module provides comprehensive post-processing formatting capabilities for Excel files
generated by the data processing pipeline. It reads format specifications from the master
configuration file and applies proper Excel formatting to output files.

Key Features:
- Reads format specifications from all Sr.no. sheets in BRD_Automation_RAW.xlsx
- Applies Date, Text, Number, and Number (%) formatting
- Handles calculation formulas from Notes sections
- Runs as final step after all pipeline processing is complete
- Completely modular and non-intrusive to existing pipeline
"""

import pandas as pd
import openpyxl
from openpyxl.styles import NamedStyle, Font, PatternFill, Alignment
from openpyxl.utils import get_column_letter
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import re

# Configure logging
logger = logging.getLogger(__name__)

@dataclass
class FormatRule:
    """Format rule for a specific column"""
    header_name: str
    sr_number: str
    required: bool
    format_type: str
    special_action: str
    example: str = ""
    notes: str = ""
    calculation_formula: str = ""

@dataclass
class FormattingResult:
    """Result of formatting a single file"""
    file_path: str
    sr_number: str
    status: str  # Success/Failed/Skipped
    columns_formatted: List[str]
    errors: List[str]
    timestamp: datetime

class FormatMappingReader:
    """Reads format specifications from all Sr.no. sheets in the master Excel file"""
    
    def __init__(self, master_excel_path: str):
        self.master_excel_path = Path(master_excel_path)
        self.logger = logging.getLogger(self.__class__.__name__)
        self.format_mappings: Dict[str, List[FormatRule]] = {}
    
    def read_all_format_specifications(self) -> Dict[str, List[FormatRule]]:
        """Read format specifications from all Sr.no. sheets"""
        try:
            wb = openpyxl.load_workbook(self.master_excel_path, read_only=True, data_only=True)
            
            # Find all Sr.no. sheets
            sr_sheets = [sheet for sheet in wb.sheetnames if sheet.startswith('Sr. No.')]
            self.logger.info(f"Found {len(sr_sheets)} Sr.no. sheets to process")
            
            for sheet_name in sr_sheets:
                sr_number = self._extract_sr_number(sheet_name)
                if sr_number:
                    rules = self._read_sheet_format_rules(wb[sheet_name], sr_number)
                    if rules:
                        self.format_mappings[sr_number] = rules
                        self.logger.info(f"Read {len(rules)} format rules for {sheet_name}")
            
            wb.close()
            self.logger.info(f"Successfully read format specifications for {len(self.format_mappings)} Sr.no. sheets")
            return self.format_mappings
            
        except Exception as e:
            self.logger.error(f"Error reading format specifications: {e}")
            raise
    
    def _extract_sr_number(self, sheet_name: str) -> str:
        """Extract SR number from sheet name"""
        try:
            # Extract number from sheet names like "Sr. No. 5", "Sr. No.7", etc.
            match = re.search(r'Sr\.\s*No\.\s*(\d+)', sheet_name)
            if match:
                return match.group(1)
            return ""
        except Exception as e:
            self.logger.warning(f"Error extracting SR number from '{sheet_name}': {e}")
            return ""
    
    def _read_sheet_format_rules(self, worksheet, sr_number: str) -> List[FormatRule]:
        """Read format rules from a specific Sr.no. sheet"""
        try:
            rules = []
            
            # Find the header row
            header_row = None
            for row_num in range(1, 10):
                cell_value = worksheet.cell(row=row_num, column=1).value
                if cell_value and 'Header Name' in str(cell_value):
                    header_row = row_num
                    break
            
            if not header_row:
                self.logger.warning(f"Could not find header row in Sr.no. {sr_number}")
                return []
            
            # Read column headers to understand the structure
            headers = {}
            for col_num in range(1, 15):  # Check up to column O
                cell_value = worksheet.cell(row=header_row, column=col_num).value
                if cell_value:
                    header_text = str(cell_value).strip().lower()
                    if 'header name' in header_text:
                        headers['header_name'] = col_num
                    elif 'required' in header_text:
                        headers['required'] = col_num
                    elif 'format' in header_text:
                        headers['format'] = col_num
                    elif 'special action' in header_text:
                        headers['special_action'] = col_num
                    elif 'example' in header_text:
                        headers['example'] = col_num
                    elif 'note' in header_text:
                        headers['notes'] = col_num
            
            # Read data rows
            for row_num in range(header_row + 1, worksheet.max_row + 1):
                header_name_cell = worksheet.cell(row=row_num, column=headers.get('header_name', 1))
                header_name = header_name_cell.value
                
                if not header_name or str(header_name).strip() == "":
                    continue
                
                # Read other columns
                required_val = worksheet.cell(row=row_num, column=headers.get('required', 2)).value
                format_val = worksheet.cell(row=row_num, column=headers.get('format', 3)).value
                special_action_val = worksheet.cell(row=row_num, column=headers.get('special_action', 4)).value
                example_val = worksheet.cell(row=row_num, column=headers.get('example', 5)).value
                notes_val = worksheet.cell(row=row_num, column=headers.get('notes', 6)).value
                
                # Check for calculation formulas in adjacent columns (like Sr.no. 5)
                calculation_formula = ""
                if headers.get('notes'):
                    # Check the column next to notes for calculation formulas
                    calc_cell = worksheet.cell(row=row_num, column=headers['notes'] + 1)
                    if calc_cell.value and str(calc_cell.value).strip():
                        calculation_formula = str(calc_cell.value).strip()
                
                rule = FormatRule(
                    header_name=str(header_name).strip(),
                    sr_number=sr_number,
                    required=str(required_val).strip().upper() == 'Y' if required_val else False,
                    format_type=str(format_val).strip() if format_val else "",
                    special_action=str(special_action_val).strip() if special_action_val else "",
                    example=str(example_val).strip() if example_val else "",
                    notes=str(notes_val).strip() if notes_val else "",
                    calculation_formula=calculation_formula
                )
                
                rules.append(rule)
            
            return rules
            
        except Exception as e:
            self.logger.error(f"Error reading format rules for Sr.no. {sr_number}: {e}")
            return []

class ExcelFormatter:
    """Applies formatting to existing Excel files based on format specifications"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self._setup_named_styles()
    
    def _setup_named_styles(self):
        """Setup named styles for consistent formatting"""
        try:
            # Create named styles for different format types
            self.date_style = NamedStyle(name="date_format")
            self.date_style.number_format = 'DD-MMM-YY'
            
            self.percentage_style = NamedStyle(name="percentage_format")
            self.percentage_style.number_format = '0.00%'
            
            self.number_style = NamedStyle(name="number_format")
            self.number_style.number_format = '0.00'
            
            self.text_style = NamedStyle(name="text_format")
            self.text_style.number_format = '@'
            
        except Exception as e:
            self.logger.warning(f"Error setting up named styles: {e}")
    
    def format_excel_file(self, file_path: str, format_rules: List[FormatRule]) -> FormattingResult:
        """Apply formatting to a single Excel file"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                return FormattingResult(
                    file_path=str(file_path),
                    sr_number=format_rules[0].sr_number if format_rules else "",
                    status="Failed",
                    columns_formatted=[],
                    errors=[f"File not found: {file_path}"],
                    timestamp=datetime.now()
                )
            
            # Load the workbook
            wb = openpyxl.load_workbook(file_path)
            
            # Assume the main data is in the first sheet or 'Data' sheet
            if 'Data' in wb.sheetnames:
                ws = wb['Data']
            else:
                ws = wb.active
            
            columns_formatted = []
            errors = []
            
            # Get header row (assumed to be row 1)
            headers = {}
            for col_num in range(1, ws.max_column + 1):
                cell_value = ws.cell(row=1, column=col_num).value
                if cell_value:
                    headers[str(cell_value).strip()] = col_num
            
            # Apply formatting based on rules
            for rule in format_rules:
                if rule.header_name in headers:
                    col_num = headers[rule.header_name]
                    success = self._apply_column_formatting(ws, col_num, rule)
                    if success:
                        columns_formatted.append(rule.header_name)
                    else:
                        errors.append(f"Failed to format column: {rule.header_name}")
            
            # Add calculated columns if specified in rules
            self._add_calculated_columns(ws, format_rules, headers)
            
            # Save the formatted file
            wb.save(file_path)
            wb.close()
            
            sr_number = format_rules[0].sr_number if format_rules else ""
            status = "Success" if not errors else "Partial"
            
            self.logger.info(f"Formatted {len(columns_formatted)} columns in {file_path}")
            
            return FormattingResult(
                file_path=str(file_path),
                sr_number=sr_number,
                status=status,
                columns_formatted=columns_formatted,
                errors=errors,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            error_msg = f"Error formatting file {file_path}: {str(e)}"
            self.logger.error(error_msg)
            return FormattingResult(
                file_path=str(file_path),
                sr_number=format_rules[0].sr_number if format_rules else "",
                status="Failed",
                columns_formatted=[],
                errors=[error_msg],
                timestamp=datetime.now()
            )

    def _apply_column_formatting(self, worksheet, col_num: int, rule: FormatRule) -> bool:
        """Apply formatting to a specific column"""
        try:
            format_type = rule.format_type.lower().strip()

            # Skip if no format specified or format is "-"
            if not format_type or format_type == "-":
                return True

            col_letter = get_column_letter(col_num)

            # Apply formatting to all data rows (skip header row)
            for row_num in range(2, worksheet.max_row + 1):
                cell = worksheet[f"{col_letter}{row_num}"]
                cell_value = cell.value

                # Skip empty cells or cells with "-" (our blank replacements)
                if not cell_value or str(cell_value).strip() in ["", "-"]:
                    continue

                if format_type == "date":
                    self._format_date_cell(cell, cell_value)
                elif format_type == "number (%)":
                    self._format_percentage_cell(cell, cell_value)
                elif format_type == "number":
                    self._format_number_cell(cell, cell_value)
                elif format_type == "text":
                    self._format_text_cell(cell, cell_value)

            return True

        except Exception as e:
            self.logger.warning(f"Error formatting column {col_num} ({rule.header_name}): {e}")
            return False

    def _format_date_cell(self, cell, value):
        """Format cell as date"""
        try:
            # If the value is already in DD-MMM-YY format from transformation, keep it
            if isinstance(value, str) and re.match(r'\d{2}-[A-Z]{3}-\d{2}', value):
                cell.value = value
                cell.number_format = '@'  # Keep as text to preserve format
            else:
                # Try to parse as date
                if isinstance(value, datetime):
                    cell.value = value
                    cell.number_format = 'DD-MMM-YY'
                else:
                    # Keep original value if can't parse as date
                    cell.value = str(value)
                    cell.number_format = '@'
        except Exception as e:
            self.logger.warning(f"Error formatting date cell: {e}")
            cell.value = str(value)

    def _format_percentage_cell(self, cell, value):
        """Format cell as percentage"""
        try:
            # Try to convert to numeric value
            if isinstance(value, str):
                # Remove any existing % symbol
                clean_value = value.replace('%', '').strip()
                numeric_value = float(clean_value)
            else:
                numeric_value = float(value)

            # If the value is already in percentage form (like 15.5 for 15.5%),
            # divide by 100 for Excel percentage format
            if numeric_value > 1:
                cell.value = numeric_value / 100
            else:
                cell.value = numeric_value

            cell.number_format = '0.00%'

        except (ValueError, TypeError):
            # If conversion fails, keep as text
            cell.value = str(value)
            cell.number_format = '@'

    def _format_number_cell(self, cell, value):
        """Format cell as number"""
        try:
            numeric_value = float(value)
            cell.value = numeric_value

            # Use appropriate number format based on value
            if numeric_value == int(numeric_value):
                cell.number_format = '0'
            else:
                cell.number_format = '0.00'

        except (ValueError, TypeError):
            # If conversion fails, keep as text
            cell.value = str(value)
            cell.number_format = '@'

    def _format_text_cell(self, cell, value):
        """Format cell as text"""
        cell.value = str(value)
        cell.number_format = '@'  # Force text format

    def _add_calculated_columns(self, worksheet, format_rules: List[FormatRule], headers: Dict[str, int]):
        """Add calculated columns based on Notes and calculation formulas"""
        try:
            # Look for rules with calculation formulas
            calc_rules = [rule for rule in format_rules if rule.calculation_formula]

            if not calc_rules:
                return

            # For each calculation rule, add the calculated column
            for rule in calc_rules:
                if rule.notes and "Final Margin" in rule.notes:
                    self._add_final_margin_column(worksheet, rule, headers)

        except Exception as e:
            self.logger.warning(f"Error adding calculated columns: {e}")

    def _add_final_margin_column(self, worksheet, rule: FormatRule, headers: Dict[str, int]):
        """Add Final Margin calculated column based on the formula in Notes"""
        try:
            # Parse the calculation formula
            # Example: "Total Margin(%)+Additional Long Margin(%)+Special Long Margin(%)+ELM Long (%)+Delivery Margin(%)"
            formula_parts = rule.calculation_formula.split('+')
            required_columns = [part.strip() for part in formula_parts]

            # Check if all required columns exist
            missing_columns = [col for col in required_columns if col not in headers]
            if missing_columns:
                self.logger.warning(f"Missing columns for calculation: {missing_columns}")
                return

            # Determine column name based on rule notes
            if "Long:" in rule.notes:
                new_column_name = "Long: Final Margin (%)"
            elif "Short:" in rule.notes:
                new_column_name = "Short: Final Margin (%)"
            else:
                new_column_name = "Final Margin (%)"

            # Add new column header
            new_col_num = worksheet.max_column + 1
            worksheet.cell(row=1, column=new_col_num, value=new_column_name)

            # Add calculated values for each row
            for row_num in range(2, worksheet.max_row + 1):
                total = 0
                valid_calculation = True

                for col_name in required_columns:
                    if col_name in headers:
                        cell_value = worksheet.cell(row=row_num, column=headers[col_name]).value
                        try:
                            if cell_value and str(cell_value).strip() not in ["", "-"]:
                                # Handle percentage values
                                if isinstance(cell_value, str) and '%' in cell_value:
                                    numeric_val = float(cell_value.replace('%', ''))
                                else:
                                    numeric_val = float(cell_value)
                                total += numeric_val
                            else:
                                # If any required value is missing, skip this row
                                valid_calculation = False
                                break
                        except (ValueError, TypeError):
                            valid_calculation = False
                            break

                if valid_calculation:
                    # Set the calculated value
                    calc_cell = worksheet.cell(row=row_num, column=new_col_num)
                    calc_cell.value = total / 100  # Convert to decimal for percentage format
                    calc_cell.number_format = '0.00%'
                else:
                    # Set as blank if calculation not possible
                    worksheet.cell(row=row_num, column=new_col_num, value="-")

            self.logger.info(f"Added calculated column: {new_column_name}")

        except Exception as e:
            self.logger.warning(f"Error adding final margin column: {e}")


class PostProcessingManager:
    """Orchestrates the post-processing formatting of Excel files"""

    def __init__(self, master_excel_path: str, output_dir: str = "output"):
        self.master_excel_path = master_excel_path
        self.output_dir = Path(output_dir)
        self.logger = logging.getLogger(self.__class__.__name__)

        # Initialize components
        self.format_reader = FormatMappingReader(master_excel_path)
        self.formatter = ExcelFormatter()

        # Results tracking
        self.formatting_results: List[FormattingResult] = []

    def format_all_output_files(self) -> List[FormattingResult]:
        """Format all Excel files in the output directory"""
        try:
            self.logger.info("Starting post-processing formatting of output files")

            # Read all format specifications
            format_mappings = self.format_reader.read_all_format_specifications()

            if not format_mappings:
                self.logger.warning("No format specifications found")
                return []

            # Find all Excel files in output directory
            excel_files = list(self.output_dir.glob("*.xlsx"))
            self.logger.info(f"Found {len(excel_files)} Excel files to format")

            # Process each file
            for excel_file in excel_files:
                # Determine which Sr.no. applies to this file
                sr_number = self._determine_sr_number_for_file(excel_file, format_mappings)

                if sr_number and sr_number in format_mappings:
                    format_rules = format_mappings[sr_number]
                    result = self.formatter.format_excel_file(str(excel_file), format_rules)
                    self.formatting_results.append(result)
                else:
                    # Create a skipped result
                    result = FormattingResult(
                        file_path=str(excel_file),
                        sr_number=sr_number or "Unknown",
                        status="Skipped",
                        columns_formatted=[],
                        errors=[f"No format rules found for SR {sr_number}"],
                        timestamp=datetime.now()
                    )
                    self.formatting_results.append(result)

            # Generate formatting summary report
            self._generate_formatting_summary()

            self.logger.info("Post-processing formatting completed")
            return self.formatting_results

        except Exception as e:
            self.logger.error(f"Error in post-processing formatting: {e}")
            raise

    def format_specific_files(self, file_paths: List[str], sr_numbers: List[str] = None) -> List[FormattingResult]:
        """Format specific Excel files with optional SR number specification"""
        try:
            self.logger.info(f"Starting formatting of {len(file_paths)} specific files")

            # Read all format specifications
            format_mappings = self.format_reader.read_all_format_specifications()

            if not format_mappings:
                self.logger.warning("No format specifications found")
                return []

            # Process each specified file
            for i, file_path in enumerate(file_paths):
                file_path = Path(file_path)

                if not file_path.exists():
                    result = FormattingResult(
                        file_path=str(file_path),
                        sr_number="Unknown",
                        status="Failed",
                        columns_formatted=[],
                        errors=[f"File not found: {file_path}"],
                        timestamp=datetime.now()
                    )
                    self.formatting_results.append(result)
                    continue

                # Determine SR number
                if sr_numbers and i < len(sr_numbers):
                    sr_number = sr_numbers[i]
                else:
                    sr_number = self._determine_sr_number_for_file(file_path, format_mappings)

                if sr_number and sr_number in format_mappings:
                    format_rules = format_mappings[sr_number]
                    result = self.formatter.format_excel_file(str(file_path), format_rules)
                    self.formatting_results.append(result)
                else:
                    result = FormattingResult(
                        file_path=str(file_path),
                        sr_number=sr_number or "Unknown",
                        status="Skipped",
                        columns_formatted=[],
                        errors=[f"No format rules found for SR {sr_number}"],
                        timestamp=datetime.now()
                    )
                    self.formatting_results.append(result)

            self.logger.info("Specific file formatting completed")
            return self.formatting_results

        except Exception as e:
            self.logger.error(f"Error formatting specific files: {e}")
            raise

    def _determine_sr_number_for_file(self, file_path: Path, format_mappings: Dict[str, List[FormatRule]]) -> str:
        """Determine which Sr.no. applies to a given file"""
        try:
            # Strategy 1: Check if filename contains SR number pattern
            filename = file_path.stem.lower()

            # Look for patterns like "sr5", "sr_5", "sr.5", etc.
            sr_match = re.search(r'sr[\._\s]*(\d+)', filename)
            if sr_match:
                sr_number = sr_match.group(1)
                if sr_number in format_mappings:
                    return sr_number

            # Strategy 2: Try to read the file and match headers with format rules
            try:
                # Read the Excel file to get headers
                df = pd.read_excel(file_path, nrows=0)  # Just read headers
                file_headers = set(df.columns.str.strip().str.lower())

                best_match_sr = ""
                best_match_score = 0

                for sr_number, rules in format_mappings.items():
                    # Get headers from format rules
                    rule_headers = set(rule.header_name.strip().lower() for rule in rules if rule.required)

                    # Calculate match score (intersection over union)
                    if rule_headers:
                        intersection = len(file_headers.intersection(rule_headers))
                        union = len(file_headers.union(rule_headers))
                        score = intersection / union if union > 0 else 0

                        if score > best_match_score and score > 0.3:  # At least 30% match
                            best_match_score = score
                            best_match_sr = sr_number

                if best_match_sr:
                    self.logger.info(f"Matched {file_path.name} to SR {best_match_sr} (score: {best_match_score:.2f})")
                    return best_match_sr

            except Exception as e:
                self.logger.warning(f"Error reading file headers for {file_path}: {e}")

            # Strategy 3: Default fallback - return empty string
            self.logger.warning(f"Could not determine SR number for {file_path}")
            return ""

        except Exception as e:
            self.logger.warning(f"Error determining SR number for {file_path}: {e}")
            return ""

    def _generate_formatting_summary(self):
        """Generate a summary report of formatting results"""
        try:
            if not self.formatting_results:
                return

            summary_data = []
            for result in self.formatting_results:
                summary_data.append({
                    'File Path': result.file_path,
                    'SR Number': result.sr_number,
                    'Status': result.status,
                    'Columns Formatted': ', '.join(result.columns_formatted) if result.columns_formatted else 'None',
                    'Errors': '; '.join(result.errors) if result.errors else 'None',
                    'Formatting Time': result.timestamp.strftime("%Y-%m-%d %H:%M:%S")
                })

            summary_df = pd.DataFrame(summary_data)

            # Generate statistics
            total_files = len(self.formatting_results)
            successful_files = len([r for r in self.formatting_results if r.status == "Success"])
            partial_files = len([r for r in self.formatting_results if r.status == "Partial"])
            failed_files = len([r for r in self.formatting_results if r.status == "Failed"])
            skipped_files = len([r for r in self.formatting_results if r.status == "Skipped"])

            stats_data = {
                'Metric': ['Total Files', 'Successfully Formatted', 'Partially Formatted', 'Failed', 'Skipped', 'Success Rate'],
                'Value': [
                    total_files,
                    successful_files,
                    partial_files,
                    failed_files,
                    skipped_files,
                    f"{((successful_files + partial_files)/total_files)*100:.1f}%" if total_files > 0 else "0%"
                ]
            }
            stats_df = pd.DataFrame(stats_data)

            # Save formatting summary report
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            summary_path = self.output_dir / f"formatting_summary_{timestamp}.xlsx"

            with pd.ExcelWriter(summary_path, engine='openpyxl') as writer:
                summary_df.to_excel(writer, sheet_name='Formatting Results', index=False)
                stats_df.to_excel(writer, sheet_name='Formatting Statistics', index=False)

            self.logger.info(f"Generated formatting summary report: {summary_path}")

        except Exception as e:
            self.logger.error(f"Error generating formatting summary: {e}")


# Integration functions for the main pipeline
def format_pipeline_output(master_excel_path: str, output_dir: str = "output") -> List[FormattingResult]:
    """
    Main integration function to format all output files from the pipeline.
    This should be called after the main pipeline processing is complete.

    Args:
        master_excel_path: Path to the BRD_Automation_RAW.xlsx file
        output_dir: Directory containing the output Excel files

    Returns:
        List of FormattingResult objects
    """
    try:
        logger.info("Starting post-processing data formatting")

        manager = PostProcessingManager(master_excel_path, output_dir)
        results = manager.format_all_output_files()

        # Print summary
        total_files = len(results)
        successful_files = len([r for r in results if r.status == "Success"])
        partial_files = len([r for r in results if r.status == "Partial"])

        logger.info(f"Formatting completed: {successful_files + partial_files}/{total_files} files processed successfully")

        return results

    except Exception as e:
        logger.error(f"Error in post-processing formatting: {e}")
        raise


def format_specific_file(file_path: str, sr_number: str, master_excel_path: str) -> FormattingResult:
    """
    Format a specific Excel file with a known SR number.

    Args:
        file_path: Path to the Excel file to format
        sr_number: SR number to use for format rules
        master_excel_path: Path to the BRD_Automation_RAW.xlsx file

    Returns:
        FormattingResult object
    """
    try:
        logger.info(f"Formatting specific file: {file_path} with SR {sr_number}")

        manager = PostProcessingManager(master_excel_path)
        results = manager.format_specific_files([file_path], [sr_number])

        return results[0] if results else FormattingResult(
            file_path=file_path,
            sr_number=sr_number,
            status="Failed",
            columns_formatted=[],
            errors=["No results returned"],
            timestamp=datetime.now()
        )

    except Exception as e:
        logger.error(f"Error formatting specific file: {e}")
        raise


# Example usage and testing
if __name__ == "__main__":
    import sys
    import argparse

    def main():
        """Main function for standalone execution"""
        parser = argparse.ArgumentParser(description="Data Formatting Post-Processor")
        parser.add_argument("master_excel", help="Path to BRD_Automation_RAW.xlsx file")
        parser.add_argument("--output-dir", default="output", help="Output directory containing Excel files")
        parser.add_argument("--file", help="Format specific file (requires --sr-number)")
        parser.add_argument("--sr-number", help="SR number for specific file formatting")

        args = parser.parse_args()

        try:
            if args.file:
                # Format specific file
                if not args.sr_number:
                    print("Error: --sr-number is required when using --file")
                    return 1

                result = format_specific_file(args.file, args.sr_number, args.master_excel)

                print(f"\nFormatting Result:")
                print(f"File: {result.file_path}")
                print(f"Status: {result.status}")
                print(f"Columns Formatted: {', '.join(result.columns_formatted) if result.columns_formatted else 'None'}")
                if result.errors:
                    print(f"Errors: {'; '.join(result.errors)}")

                return 0 if result.status in ["Success", "Partial"] else 1

            else:
                # Format all files in output directory
                results = format_pipeline_output(args.master_excel, args.output_dir)

                print(f"\nFormatting Summary:")
                print(f"Total files: {len(results)}")

                for status in ["Success", "Partial", "Failed", "Skipped"]:
                    count = len([r for r in results if r.status == status])
                    if count > 0:
                        print(f"{status}: {count}")

                print(f"\nDetailed Results:")
                for result in results:
                    status_symbol = {"Success": "✓", "Partial": "⚠", "Failed": "✗", "Skipped": "○"}
                    symbol = status_symbol.get(result.status, "?")
                    print(f"{symbol} {Path(result.file_path).name} (SR {result.sr_number}) - {result.status}")

                    if result.columns_formatted:
                        print(f"    Formatted: {', '.join(result.columns_formatted)}")
                    if result.errors:
                        for error in result.errors:
                            print(f"    Error: {error}")

                failed_count = len([r for r in results if r.status == "Failed"])
                return 0 if failed_count == 0 else 1

        except Exception as e:
            print(f"Error: {e}")
            return 1

    sys.exit(main())
