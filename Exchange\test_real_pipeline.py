#!/usr/bin/env python3
"""
Test the fixes with the real pipeline using actual files
"""

from data_pipeline import DataProcessingPipeline
import pandas as pd

def test_real_pipeline():
    """Test the fixes with the actual pipeline"""
    print("=== Testing Real Pipeline with Fixes ===")
    
    try:
        # Create pipeline
        pipeline = DataProcessingPipeline("BRD file Automation.xlsx", "test_output_fixes")
        
        # Read file list to see what exchanges are available
        files = pipeline.master_excel_reader.read_file_list()
        
        print(f"Found {len(files)} files in master Excel")
        print("\nFirst 5 files with their exchange values:")
        for i, file_info in enumerate(files[:5]):
            print(f"  {i+1}. SR {file_info.sr_number}: {file_info.file_name}")
            print(f"      Exchange: '{file_info.exchange}'")
            print(f"      Location: {file_info.file_location}")
        
        # Test with the first file that has an exchange value
        test_file = None
        for file_info in files:
            if file_info.exchange and file_info.exchange.strip():
                test_file = file_info
                break
        
        if test_file:
            print(f"\nTesting with file: {test_file.file_name}")
            print(f"Exchange: {test_file.exchange}")
            print(f"SR Number: {test_file.sr_number}")
            
            # Process just this one file
            result = pipeline.process_single_file(test_file)
            
            print(f"\nProcessing result:")
            print(f"Status: {result.status}")
            print(f"Errors: {result.errors}")
            print(f"Output file: {result.output_file}")
            
            if result.status == "Success" and result.output_file:
                # Read the output file to verify our fixes
                print(f"\nReading output file to verify fixes...")
                
                try:
                    df = pd.read_excel(result.output_file, sheet_name='Data')
                    print(f"Output file shape: {df.shape}")
                    print(f"Columns: {list(df.columns)}")
                    
                    # Check if Exchange column is first
                    if len(df.columns) > 0 and df.columns[0] == 'Exchange':
                        print("✓ Exchange column is correctly placed as first column")
                        
                        # Check if all Exchange values are correct
                        exchange_values = df['Exchange'].unique()
                        if len(exchange_values) == 1 and exchange_values[0] == test_file.exchange:
                            print(f"✓ Exchange column has correct value: '{exchange_values[0]}'")
                        else:
                            print(f"✗ Exchange column has unexpected values: {exchange_values}")
                    else:
                        print(f"✗ Exchange column is not first. First column: {df.columns[0] if len(df.columns) > 0 else 'None'}")
                    
                    # Show first few rows
                    print(f"\nFirst 3 rows of output:")
                    print(df.head(3).to_string())
                    
                except Exception as e:
                    print(f"Error reading output file: {e}")
            else:
                print("Processing failed or no output file generated")
        else:
            print("No files found with exchange values to test")
            
    except Exception as e:
        print(f"Error in real pipeline test: {e}")

if __name__ == "__main__":
    test_real_pipeline()
