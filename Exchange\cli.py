#!/usr/bin/env python3
"""
Command Line Interface for the Data Processing Pipeline

This script provides a comprehensive CLI for running the automated data processing pipeline.
It supports various options for configuration, logging, and output management.
"""

import argparse
import sys
import logging
from pathlib import Path
from datetime import datetime
from data_pipeline import DataProcessingPipeline

def setup_logging(log_level: str, log_file: str = None):
    """Setup logging configuration"""

    # Convert string level to logging constant
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f'Invalid log level: {log_level}')

    # Configure logging
    handlers = [logging.StreamHandler()]

    if log_file:
        handlers.append(logging.FileHandler(log_file))

    logging.basicConfig(
        level=numeric_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=handlers
    )

def validate_inputs(args):
    """Validate command line arguments"""

    # Check if master Excel file exists
    if not Path(args.excel_file).exists():
        print(f"Error: Master Excel file not found: {args.excel_file}")
        return False

    # Create output directory if it doesn't exist
    output_dir = Path(args.output_dir)
    try:
        output_dir.mkdir(parents=True, exist_ok=True)
    except Exception as e:
        print(f"Error: Cannot create output directory {args.output_dir}: {e}")
        return False

    return True

def print_banner():
    """Print application banner"""
    print("=" * 70)
    print("           AUTOMATED DATA PROCESSING PIPELINE")
    print("                    Version 1.0")
    print("=" * 70)
    print()

def print_summary(results):
    """Print processing summary"""

    total_files = len(results)
    successful_files = len([r for r in results if r.status == "Success"])
    failed_files = total_files - successful_files

    print("\n" + "=" * 50)
    print("PROCESSING SUMMARY")
    print("=" * 50)
    print(f"Total files processed: {total_files}")
    print(f"Successful: {successful_files}")
    print(f"Failed: {failed_files}")

    if total_files > 0:
        success_rate = (successful_files / total_files) * 100
        print(f"Success rate: {success_rate:.1f}%")

    print("\nDetailed Results:")
    for result in results:
        status_symbol = "✓" if result.status == "Success" else "✗"
        print(f"{status_symbol} SR {result.sr_number}: {result.file_name} - {result.status}")

        if result.errors:
            for error in result.errors:
                print(f"    Error: {error}")

        if result.status == "Success":
            print(f"    Columns: {len(result.columns_transformed)} transformed, {len(result.columns_dropped)} dropped")
            if result.output_file:
                print(f"    Output: {result.output_file}")

def main():
    """Main CLI function"""

    parser = argparse.ArgumentParser(
        description="Automated Data Processing Pipeline",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s -e master.xlsx                    # Basic usage
  %(prog)s -e master.xlsx -o output/        # Custom output directory
  %(prog)s -e master.xlsx -v                # Verbose logging
  %(prog)s -e master.xlsx --dry-run         # Preview without processing
  %(prog)s -e master.xlsx --sr 1,2,10       # Process specific SR numbers only
        """
    )

    # Required arguments
    parser.add_argument(
        '-e', '--excel-file',
        required=True,
        help='Path to the master Excel workbook'
    )

    # Optional arguments
    parser.add_argument(
        '-o', '--output-dir',
        default='output',
        help='Output directory for processed files (default: output)'
    )

    parser.add_argument(
        '-l', '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level (default: INFO)'
    )

    parser.add_argument(
        '--log-file',
        help='Log file path (default: pipeline_YYYYMMDD_HHMMSS.log)'
    )

    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='Enable verbose output (equivalent to --log-level DEBUG)'
    )

    parser.add_argument(
        '-q', '--quiet',
        action='store_true',
        help='Suppress output except errors (equivalent to --log-level ERROR)'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Preview files to be processed without actually processing them'
    )

    parser.add_argument(
        '--sr',
        help='Comma-separated list of SR numbers to process (e.g., 1,2,10). If not specified, all files will be processed'
    )

    parser.add_argument(
        '--no-banner',
        action='store_true',
        help='Suppress the application banner'
    )

    parser.add_argument(
        '--version',
        action='version',
        version='Data Processing Pipeline 1.0'
    )

    # Parse arguments
    args = parser.parse_args()

    # Handle verbose/quiet flags
    if args.verbose:
        args.log_level = 'DEBUG'
    elif args.quiet:
        args.log_level = 'ERROR'

    # Setup default log file if not specified
    if not args.log_file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        args.log_file = f"pipeline_{timestamp}.log"

    # Setup logging
    try:
        setup_logging(args.log_level, args.log_file)
    except ValueError as e:
        print(f"Error: {e}")
        return 1

    # Print banner
    if not args.no_banner:
        print_banner()

    # Validate inputs
    if not validate_inputs(args):
        return 1

    # Parse SR numbers if specified
    sr_filter = None
    if args.sr:
        try:
            sr_filter = [s.strip() for s in args.sr.split(',')]
            print(f"Processing only SR numbers: {', '.join(sr_filter)}")
        except Exception as e:
            print(f"Error parsing SR numbers: {e}")
            return 1

    try:
        # Create pipeline
        print(f"Initializing pipeline...")
        print(f"Master Excel: {args.excel_file}")
        print(f"Output directory: {args.output_dir}")
        print(f"Log file: {args.log_file}")
        print()

        pipeline = DataProcessingPipeline(args.excel_file, args.output_dir)

        # Read file list
        files = pipeline.master_excel_reader.read_file_list()

        # Filter by SR numbers if specified
        if sr_filter:
            files = [f for f in files if f.sr_number in sr_filter]

        print(f"Found {len(files)} file(s) to process:")
        for i, file_info in enumerate(files):
            print(f"  {i+1}. SR {file_info.sr_number}: {file_info.file_name}")
            print(f"      Location: {file_info.file_location}")

        if args.dry_run:
            print("\nDry run completed. No files were processed.")
            return 0

        if not files:
            print("No files to process.")
            return 0

        # Confirm processing
        if not args.quiet:
            response = input(f"\nProceed with processing {len(files)} file(s)? [Y/n]: ")
            if response.lower() in ['n', 'no']:
                print("Processing cancelled.")
                return 0

        # Process files
        print(f"\nStarting processing...")
        results = []

        for i, file_info in enumerate(files):
            print(f"\nProcessing {i+1}/{len(files)}: {file_info.file_name}")
            result = pipeline.process_single_file(file_info)
            results.append(result)

            # Print immediate result
            if result.status == "Success":
                print(f"  ✓ Success - Output: {result.output_file}")
            else:
                print(f"  ✗ Failed - {'; '.join(result.errors)}")

        # Generate summary report
        pipeline.results = results
        pipeline._generate_summary_report()

        # Print final summary
        print_summary(results)

        # Return appropriate exit code
        failed_count = len([r for r in results if r.status == "Failed"])
        return 1 if failed_count > 0 else 0

    except KeyboardInterrupt:
        print("\nProcessing interrupted by user.")
        return 1
    except Exception as e:
        print(f"Error: {e}")
        logging.exception("Unexpected error occurred")
        return 1

if __name__ == "__main__":
    sys.exit(main())